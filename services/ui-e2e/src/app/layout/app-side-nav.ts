import { Page } from '@playwright/test';
import { AbstractElementBase } from '@ui-adk/components/abstract-element-base';
import { isMenuChildItem, MenuChildItem, MenuGroupItem, MenuItem } from '@ui-adk/types/menu-item';
import { AppSideNavMenuItem } from './app-side-nav-menu-item';
import { AppSideNavMenuGroup } from './app-side-nav-menu-group';

const MENU_TAG = 'ul.cds--side-nav__items:data-test-id';

/**
 * Application side nav menu wrapper.
 */
export class AppSideNav extends AbstractElementBase {
  /**
   *
   */
  constructor(page: Page) {
    super(page, page.locator(MENU_TAG));
  }

  public async itemExists(item: MenuItem) {
    if (isMenuChildItem(item) === true) {
      /**
       * Expand the menu group...
       */
      const child = item as MenuChildItem;
      for await (const pathItem of child.path) {
        const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(pathItem));
        await menuGroup.expand();
      }

      const menuItem = new AppSideNavMenuItem(this.page, this.locator.getByTestId(child.id));
      return await menuItem.isVisible();
    }

    const groupItem = item as MenuGroupItem;
    const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(groupItem.id));
    return await menuGroup.isVisible();
  }

  /**
   *
   */
  public async selectItem(item: MenuItem) {
    if (isMenuChildItem(item) === true) {
      /**
       * Expand the menu group...
       */
      const child = item as MenuChildItem;
      for await (const pathItem of child.path) {
        const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(pathItem));
        await menuGroup.expand();
      }

      /**
       * Selection the menu item...
       */
      const menuItem = new AppSideNavMenuItem(this.page, this.locator.getByTestId(child.id));
      await menuItem.select();
    }
  }
}
