import { Page } from '@playwright/test';
import { AbstractElementBase } from '@ui-adk/components/abstract-element-base';
import { isMenuChildItem, MenuChildItem, MenuGroupItem, MenuItem } from '@ui-adk/types/menu-item';
import { AppSideNavMenuItem } from './app-side-nav-menu-item';
import { AppSideNavMenuGroup } from './app-side-nav-menu-group';

// Target the main navigation menu specifically using data-testid
// This avoids conflicts with the favorites section which also uses SideNavItems
const MENU_TAG = '[data-testid="main-navigation-menu"]';

/**
 * Application side nav menu wrapper.
 * Targets the main navigation menu, excluding the favorites section.
 */
export class AppSideNav extends AbstractElementBase {
  /**
   *
   */
  constructor(page: Page) {
    super(page, page.locator(MENU_TAG));
  }

  public async itemExists(item: MenuItem) {
    if (isMenuChildItem(item) === true) {
      /**
       * Expand the menu group...
       */
      const child = item as MenuChildItem;
      for await (const pathItem of child.path) {
        const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(pathItem));
        await menuGroup.expand();
      }

      const menuItem = new AppSideNavMenuItem(this.page, this.locator.getByTestId(child.id));
      return await menuItem.isVisible();
    }

    const groupItem = item as MenuGroupItem;
    const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(groupItem.id));
    return await menuGroup.isVisible();
  }

  /**
   *
   */
  public async selectItem(item: MenuItem) {
    if (isMenuChildItem(item) === true) {
      /**
       * Expand the menu group...
       */
      const child = item as MenuChildItem;
      for await (const pathItem of child.path) {
        const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(pathItem));
        await menuGroup.expand();
      }

      /**
       * Selection the menu item...
       */
      const menuItem = new AppSideNavMenuItem(this.page, this.locator.getByTestId(child.id));
      await menuItem.select();
    }
  }
}
