import type { MenuItem } from "../types";
import { NestedMenu } from "./nested-menu";

interface MenuItemProps {
  item: MenuItem;
  isPathActive: (path: string) => boolean;
  onNavigate: (path: string) => void;
  depth?: number;
}

export const RecursiveMenuItem = ({
  item,
  isPathActive,
  onNavigate,
  depth = 0,
}: MenuItemProps) => {
  return (
    <NestedMenu
      item={item}
      isPathActive={isPathActive}
      onNavigate={onNavigate}
      depth={depth}
    />
  );
};
