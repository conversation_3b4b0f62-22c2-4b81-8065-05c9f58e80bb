import { ChevronDown } from "@carbon/icons-react";
import { useState } from "react";
import { iconLookup } from "../../../components/dynamic-icon/dynamic-icon";
import type { MenuItem } from "../types";
import styles from "./nested-menu.module.scss";

interface NestedMenuProps {
  item: MenuItem;
  isPathActive: (path: string) => boolean;
  onNavigate: (path: string) => void;
  depth: number;
  hideIcon?: boolean;
}

export const NestedMenu = ({
  item,
  isPathActive,
  onNavigate,
  depth,
  hideIcon = false,
}: NestedMenuProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const itemPath = item.link || "/ict-page-not-implemented";
  const isItemActive = isPathActive(itemPath);
  const hasChildren = item.children && item.children.length > 0;

  const handleClick = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
    if (itemPath !== "/ict-page-not-implemented") {
      onNavigate(itemPath);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleClick();
    }
  };

  return (
    <div data-testid={item.id} className={styles.nestedMenu}>
      <button
        type="button"
        className={`${styles.menuItem} ${isItemActive ? styles.active : ""} ${
          isExpanded ? styles.expanded : ""
        } ${styles[`depth${depth}`]}`}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
      >
        <div className={styles.menuContent}>
          {item.icon && !(item.hideIcon ?? hideIcon) && (
            <div className={styles.icon}>{iconLookup(item.icon)}</div>
          )}
          <span className={styles.label}>{item.label}</span>
          {hasChildren && (
            <ChevronDown
              size={16}
              className={`${styles.chevron} ${isExpanded ? styles.expanded : ""}`}
            />
          )}
        </div>
      </button>
      {hasChildren && isExpanded && (
        <div className={styles.children}>
          {item.children?.map((child) => (
            <NestedMenu
              key={child.id}
              item={child}
              isPathActive={isPathActive}
              onNavigate={onNavigate}
              depth={depth + 1}
              hideIcon={
                typeof child.hideIcon === "boolean" ? child.hideIcon : hideIcon
              }
            />
          ))}
        </div>
      )}
    </div>
  );
};
