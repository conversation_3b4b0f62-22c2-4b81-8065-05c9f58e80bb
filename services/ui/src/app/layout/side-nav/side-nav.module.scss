@use "@carbon/styles/scss/theme";
@use "@carbon/styles/scss/spacing";

.sideNavContainer {
  width: 300px;
  height: calc(100vh - 48px);
  transition: width 0.3s ease;
  flex-shrink: 0;
  overflow-y: auto;
  border-right: 1px solid theme.$border-subtle-00;
  display: flex;
  flex-direction: column;
  background-color: var(--cds-layer);
}

.sideNavCollapsed {
  width: 0;
  overflow: hidden;
  border-right: none;
}

/* Search container and dropdown styles */
.searchContainer {
  padding: 1rem 1rem 0 1rem;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: var(--cds-layer);

  :global(.cds--search-input) {
    background-color: var(--cds-field-02);
  }

  :global(.cds--search-close) {
    &::before {
      background-color: var(--cds-field-02);
    }
  }
}

.searchDropdown {
  position: absolute;
  top: 100%;
  left: 1rem;
  right: 1rem;
  background-color: var(--cds-field-02);
  border: 1px solid var(--cds-border-subtle);
  border-radius: 0 0 2px 2px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.searchResults {
  > * {
    position: relative;
    
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: spacing.$spacing-05;
      right: spacing.$spacing-05;
      height: 1px;
      background-color: var(--cds-border-subtle-01);
    }
  }
}

/* Menu item styles */
.searchMenuItem {
  width: 100%;
}

.searchMenuButton {
  width: 100%;
  background: none;
  border: none;
  padding: spacing.$spacing-03 spacing.$spacing-05;
  text-align: left;
  cursor: pointer;
  color: var(--cds-text-primary);
  min-height: 32px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: var(--cds-layer-hover);
  }
}

.searchMenuContent {
  display: flex;
  align-items: center;
  width: 100%;
}

.searchMenuLabel {
  flex: 1;
  font-size: 0.875rem;
  
  strong {
    font-weight: 600;
    color: var(--cds-text-primary);
  }
}

/* Navigation content and loading states */
.navContent {
  flex: 1;
  overflow-y: auto;
}

.loadingContainer {
  padding: spacing.$spacing-04;
}

/* No results and active states */
.noResultsContainer {
  padding: spacing.$spacing-05;
  text-align: center;
  color: var(--cds-text-secondary);
}

.noResultsText {
  margin: 0;
  font-size: 0.875rem;
  padding: spacing.$spacing-05;
  color: var(--cds-text-secondary);
  text-align: center;
}

.activeLink {
  font-weight: 600;
}

.routerLink {
  text-decoration: none;
  color: inherit;
  display: block;

  // This ensures the active indicator spans the full height
  :global(.cds--side-nav__link) {
    height: 100%;
  }

  // This ensures our active state is visible
  :global(.cds--side-nav__link[aria-current="page"]),
  :global(.cds--side-nav__link--current) {
    background-color: theme.$layer-active-01;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
    }
  }
}

.nestedMenuContainer {
  position: relative;
  padding-bottom: 1rem;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background-color: var(--cds-border-subtle-01);
  }
}