import { SideNavItems, SkeletonText, Search } from "@carbon/react";
import { useNavigate } from "react-router";
import { useMenu } from "../../config/menu/use-menu";
import { RecursiveMenuItem } from "./components/menu-item";
import styles from "./side-nav.module.scss";
import type { MenuItem, SideNavProps } from "./types";
import { useIsPathActive } from "./utils";
import { useTranslation } from "react-i18next";
import { useState, useMemo, useRef, useEffect } from "react";
import { filterHierarchicalItems } from "../../utils/active-search-util";
import { useFavorites } from "../../config/hooks/use-favorites";
import { NestedMenu } from "./components/nested-menu";

/**
 * SideNav component that provides a collapsible navigation menu with search functionality
 * @param isOpen - Controls whether the side nav is expanded or collapsed
 */
export function SideNav({ isOpen }: SideNavProps) {
  const { t } = useTranslation();
  const { menuItems, isLoading } = useMenu(t);
  // State and hooks initialization
  const isPathActive = useIsPathActive();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const { favorites, isLoading: favoritesLoading } = useFavorites();

  // Helper to create a lookup map from menu tree for efficient item retrieval
  const createMenuItemLookupMap = (
    items: MenuItem[],
    map: Record<string, MenuItem> = {},
  ) => {
    for (const item of items) {
      if (!item) continue;
      map[item.id] = item;
      if (item.children) createMenuItemLookupMap(item.children, map);
    }
    return map;
  };

  const menuItemLookupMap = useMemo(
    () => createMenuItemLookupMap(menuItems || []),
    [menuItems],
  );

  // Map favorite IDs to menu items and filter out any that don't exist in the menu
  const favoriteMenuItems = useMemo(() => {
    return favorites.map((id) => menuItemLookupMap[id]).filter(Boolean);
  }, [favorites, menuItemLookupMap]);

  /**
   * Handles navigation to a new route and resets search state
   * @param path - The route path to navigate to
   */
  const handleNavigation = (path: string) => {
    navigate(path);
    setIsDropdownOpen(false);
    setSearchQuery("");
  };

  /**
   * Filters menu items based on search query while maintaining hierarchy
   */
  const matchingItems = useMemo(() => {
    if (!searchQuery) return [];

    return filterHierarchicalItems<MenuItem>(
      menuItems,
      searchQuery,
      (item) => item.children,
      (item) => item.label,
      (item) =>
        item.link !== undefined && item.link !== "/ict-page-not-implemented",
    );
  }, [menuItems, searchQuery]);

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  /**
   * Highlights matching text in search results
   * @param text - The text to highlight matches in
   * @returns highlighted matches
   */
  const highlightMatch = (text: string) => {
    if (!searchQuery) return text;
    const regex = new RegExp(`(${searchQuery})`, "gi");
    return text
      .split(regex)
      .map((part, i) =>
        regex.test(part) ? (
          <strong key={i}>{part}</strong>
        ) : (
          <span key={i}>{part}</span>
        ),
      );
  };

  /**
   * Renders a single menu item with its children
   * @param item - The menu item to render
   * @returns the menu item
   */
  const renderMenuItem = (item: MenuItem) => {
    const handleItemClick = () => {
      if (item.link && item.link !== "/ict-page-not-implemented") {
        handleNavigation(item.link);
      }
    };

    return (
      <div key={item.id} className={styles.searchMenuItem}>
        <button
          type="button"
          className={styles.searchMenuButton}
          onClick={handleItemClick}
        >
          <div className={styles.searchMenuContent}>
            <span className={styles.searchMenuLabel}>
              {highlightMatch(item.label)}
            </span>
          </div>
        </button>
      </div>
    );
  };

  return (
    <div
      className={`${styles.sideNavContainer} ${
        !isOpen ? styles.sideNavCollapsed : ""
      }`}
    >
      <div className={styles.navContent}>
        {/* Search Bar with Dropdown */}
        <div className={styles.searchContainer} ref={searchContainerRef}>
          <Search
            size="sm"
            labelText="Search Pages"
            placeholder="Search Pages"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setIsDropdownOpen(true);
            }}
            onFocus={() => setIsDropdownOpen(true)}
          />
          {isDropdownOpen && searchQuery && (
            <div className={styles.searchDropdown}>
              {matchingItems.length > 0 ? (
                <div className={styles.searchResults}>
                  {matchingItems.map(renderMenuItem)}
                </div>
              ) : (
                <div className={styles.noResultsText}>
                  No results found for &quot;{searchQuery}&quot;
                </div>
              )}
            </div>
          )}
        </div>

        {/* User Favorites */}
        {!favoritesLoading && favoriteMenuItems.length > 0 && (
          <SideNavItems>
            <li>
              <div className={styles.nestedMenuContainer}>
                <NestedMenu
                  item={{
                    id: "favorites-section",
                    label: "Favorites",
                    icon: "star",
                    children: favoriteMenuItems.map((item) => ({
                      ...item,
                      hideIcon: true,
                    })),
                  }}
                  isPathActive={isPathActive}
                  onNavigate={handleNavigation}
                  depth={0}
                />
              </div>
            </li>
          </SideNavItems>
        )}
        {/* Main Navigation Menu */}
        {isLoading ? (
          <div className={styles.loadingContainer}>
            <SkeletonText width="80%" />
            <SkeletonText width="60%" />
            <SkeletonText width="70%" />
          </div>
        ) : (
          <SideNavItems>
            {menuItems.map((item) => (
              <li key={item.id}>
                <RecursiveMenuItem
                  item={item as MenuItem}
                  isPathActive={isPathActive}
                  onNavigate={handleNavigation}
                />
              </li>
            ))}
          </SideNavItems>
        )}
      </div>
    </div>
  );
}
