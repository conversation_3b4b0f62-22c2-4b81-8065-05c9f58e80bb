import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ArrowsVertical } from "@carbon/icons-react";
import { Loading } from "@carbon/react";
import type {
  Cell,
  ColumnFiltersState,
  HeaderContext,
  HeaderGroup,
  PaginationState,
  Row,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useEffect, useMemo, useState } from "react";
import { DatagridExportModal } from "./components/datagrid-export-modal/datagrid-export-modal";
import { DatagridHeader } from "./components/datagrid-header/datagrid-header";
import { DatagridPagination } from "./components/datagrid-pagination/datagrid-pagination";
import styles from "./datagrid.module.scss";
import type {
  DatagridProps,
  FilterState,
  FilterValue,
  LayoutDensity,
} from "./types";
import { useTranslation } from "react-i18next";
import { WidgetNotification } from "../widget-container/widget-notification";

/**
 * Datagrid component provides a flexible data table with sorting, filtering, pagination,
 * and row selection capabilities. It supports both client-side and server-side data processing.
 *
 * @template T - The type of data objects in the table
 * @param columns - Column definitions for the table
 * @param data - Array of data objects to display
 * @param mode - Data processing mode: "client" or "server"
 * @param enableSelection - Whether to enable row selection
 * @param rowSelection - Current row selection state
 * @param onRowSelectionChange - Callback when row selection changes
 * @param onExport - Callback for export functionality
 * @param onPageChange - Callback when page changes (server mode)
 * @param onSort - Callback when sorting changes (server mode)
 * @param onFilter - Callback when filters change
 * @param initialPagination - Initial pagination state
 * @param initialSorting - Initial sorting state
 * @param initialColumnVisibility - Initial column visibility state
 * @param initialDensity - Initial layout density
 * @param totalRows - Total number of rows (for server mode)
 * @param isLoading - Whether data is currently loading
 * @param error - Error message if data loading failed
 * @param className - Additional CSS class
 * @param showExportButton - Whether to show the export button
 * @param showPagination - Whether to show pagination controls
 * @param showHeader - Whether to show the table header
 * @param showRefreshButton - Whether to show the refresh button
 * @param onRefreshClick - Callback for refresh functionality
 * @param onClearSelection - Callback to clear row selection
 */
export function Datagrid<T extends object>({
  columns,
  data = [],
  mode = "client",
  enableSelection = false,
  rowSelection = {},
  onRowSelectionChange,
  onExport,
  exportOptions,
  onExportAction,
  onPageChange,
  onSort,
  onFilter,
  initialPagination = { pageIndex: 0, pageSize: 10 },
  initialSorting = [],
  initialColumnVisibility = {},
  initialDensity = "default",
  totalRows = 0,
  isLoading = false,
  error,
  className,
  showExportButton = true,
  showPagination = true,
  showHeader = true,
  showRefreshButton = false,
  onRefreshClick,
  onClearSelection,
}: DatagridProps<T>) {
  const { t } = useTranslation();
  // State management
  const [globalFilter, setGlobalFilter] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>(initialSorting);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    initialColumnVisibility,
  );
  const [pagination, setPagination] =
    useState<PaginationState>(initialPagination);
  const [density, setDensity] = useState<LayoutDensity>(initialDensity);

  // Add state for export modal
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  /**
   * Create table columns with optional selection column
   */
  const tableColumns = useMemo(() => {
    if (!enableSelection) return columns;

    return [
      {
        id: "selection",
        header: ({ table }: HeaderContext<T, unknown>) => (
          <input
            type="checkbox"
            checked={table.getIsAllRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
            aria-label="Select all rows"
          />
        ),
        cell: ({ row }: { row: Row<T> }) => (
          <input
            type="checkbox"
            checked={row.getIsSelected()}
            onChange={row.getToggleSelectedHandler()}
            aria-label={`Select row ${row.id}`}
          />
        ),
        enableSorting: false,
        enableFiltering: false,
        size: 40,
      },
      ...columns,
    ];
  }, [columns, enableSelection]);

  /**
   * Initialize and configure the TanStack table
   */
  const table = useReactTable({
    data,
    columns: tableColumns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      columnVisibility,
      pagination: showPagination
        ? pagination
        : { pageIndex: 0, pageSize: data.length },
      rowSelection: enableSelection ? rowSelection : {},
    },
    pageCount:
      mode === "server"
        ? Math.ceil(totalRows / pagination.pageSize)
        : undefined,
    manualPagination: mode === "server",
    manualSorting: mode === "server",
    manualFiltering: mode === "server",
    enableRowSelection: enableSelection,
    onRowSelectionChange: onRowSelectionChange,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(), // Always enable sorting for both client and server modes
    getFilteredRowModel: getFilteredRowModel(), // Always enable filtering for both client and server modes
    getPaginationRowModel: showPagination ? getPaginationRowModel() : undefined, // Only enable pagination if showPagination is true
    columnResizeMode: "onChange", // Update column sizes during resize instead of after
    enableColumnResizing: true,
  });

  /**
   * Handle server-side pagination
   */
  useEffect(() => {
    if (mode === "server") {
      onPageChange?.(pagination);
    }
  }, [mode, pagination, onPageChange]);

  /**
   * Handle server-side sorting
   */
  useEffect(() => {
    if (mode === "server") {
      onSort?.(sorting);
    }
  }, [mode, sorting, onSort]);

  /**
   * Handle filtering (both client and server modes)
   */
  useEffect(() => {
    // Create filter state object
    const filterState: FilterState = {
      globalFilter,
      filters: columnFilters.reduce<Record<string, FilterValue>>(
        (acc, filter) => {
          acc[filter.id] = filter.value as FilterValue;
          return acc;
        },
        {},
      ),
    };

    if (mode === "server") {
      // Debounce the filter creation to prevent excessive API calls
      const finalFilters = setTimeout(() => {
        onFilter?.(filterState);
      }, 1000);
      return () => {
        clearTimeout(finalFilters);
      };
    } else {
      // Call onFilter immediately for client mode
      onFilter?.(filterState);
    }
  }, [globalFilter, columnFilters, onFilter]);

  /**
   * Get visible columns for column visibility menu
   */
  const visibleColumns = useMemo(() => {
    return table
      .getAllLeafColumns()
      .filter((col) => col.id !== "selection")
      .map((col) => ({
        id: col.id,
        label:
          typeof col.columnDef.header === "string"
            ? col.columnDef.header
            : col.id,
      }));
  }, [table.getAllLeafColumns]);

  // Calculate actual total rows based on mode
  const actualTotalRows = mode === "server" ? totalRows : data.length;

  /**
   * Get CSS class for current density setting
   */
  const densityClass = useMemo(() => {
    switch (density) {
      case "compact":
        return styles.compact;
      case "relaxed":
        return styles.relaxed;
      default:
        return styles.default;
    }
  }, [density]);

  // UI state flags
  const isError = error;
  const isNoData = !isLoading && !isError && actualTotalRows === 0;

  // Calculate number of selected rows
  const selectedRowCount = enableSelection
    ? Object.values(rowSelection).filter(Boolean).length
    : 0;

  // Handle clearing all row selections
  const handleClearSelection = () => {
    if (onClearSelection) {
      onClearSelection();
    } else if (onRowSelectionChange) {
      onRowSelectionChange({});
    }
  };

  /**
   * Render loading state
   */
  const renderLoading = () => {
    return (
      <div className={styles.loadingOverlay}>
        <Loading
          active
          description={t("datagrid.loadingData", "Loading data...")}
          withOverlay={false}
        />
      </div>
    );
  };

  /**
   * Render error state
   */
  const renderError = () => {
    return (
      <div className={styles.errorContainer}>
        <WidgetNotification
          title={t(
            "datagrid.errorLoadingData",
            "Error loading data: {{error}}",
            {
              error: error,
            },
          )}
          kind="error"
        />
      </div>
    );
  };

  /**
   * Render empty state
   */
  const renderNoData = () => {
    return (
      <div className={styles.emptyState}>
        <WidgetNotification
          title={t("datagrid.noDataAvailable", "No data available")}
          kind="info"
        />
      </div>
    );
  };

  /**
   * Render the table with headers and rows
   */
  const renderTable = () => {
    return (
      <table
        data-testid="data-grid-table"
        className={`${styles.table} ${densityClass}`}
      >
        <thead>
          {table.getHeaderGroups().map((headerGroup: HeaderGroup<T>) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  colSpan={header.colSpan}
                  style={{
                    width: header.getSize() + 40,
                    position: "relative",
                  }}
                  className={header.column.getCanSort() ? styles.sortable : ""}
                  data-sorted={header.column.getIsSorted() ? "true" : "false"}
                  data-column-id={header.column.id}
                  title={
                    header.column.getIsSorted()
                      ? `${header.column.columnDef.header as string} (${
                          header.column.getIsSorted() === "asc"
                            ? t("datagrid.ascending", "Ascending")
                            : t("datagrid.descending", "Descending")
                        })`
                      : (header.column.columnDef.header as string)
                  }
                >
                  <div
                    className={styles.headerContent}
                    onClick={
                      header.column.getCanSort()
                        ? header.column.getToggleSortingHandler()
                        : undefined
                    }
                    onKeyDown={(e) => {
                      if (
                        header.column.getCanSort() &&
                        (e.key === "Enter" || e.key === " ")
                      ) {
                        e.preventDefault();
                        const handler = header.column.getToggleSortingHandler();
                        if (handler) {
                          handler(e);
                        }
                      }
                    }}
                    tabIndex={header.column.getCanSort() ? 0 : undefined}
                    role={header.column.getCanSort() ? "button" : undefined}
                  >
                    {header.isPlaceholder ? null : (
                      <>
                        <span>
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                        </span>
                        {header.column.getCanSort() && (
                          <span className={styles.sortIcon}>
                            {header.column.getIsSorted() ? (
                              {
                                asc: <ArrowUp />,
                                desc: <ArrowDown />,
                              }[header.column.getIsSorted() as string]
                            ) : (
                              <ArrowsVertical />
                            )}
                          </span>
                        )}
                      </>
                    )}
                  </div>
                  {header.column.getCanResize() && (
                    <div
                      className={`${styles.resizer} ${
                        header.column.getIsResizing() ? styles.isResizing : ""
                      }`}
                      onMouseDown={header.getResizeHandler()}
                      onTouchStart={header.getResizeHandler()}
                      onClick={(e) => e.stopPropagation()}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          e.preventDefault();
                        }
                      }}
                      tabIndex={0}
                      role="separator"
                      aria-orientation="vertical"
                      aria-valuenow={header.getSize()}
                    />
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row: Row<T>) => (
            <tr key={row.id}>
              {row.getVisibleCells().map((cell: Cell<T, unknown>) => (
                <td key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  /**
   * Handle export functionality
   */
  const handleExport = () => {
    // If custom export handler is provided, use it
    if (onExport) {
      onExport();
      return;
    }

    // Only open the built-in export modal for client-side tables
    if (mode === "client") {
      setIsExportModalOpen(true);
    }
  };

  // Get column information for the export modal
  const exportColumns = useMemo(() => {
    return columns
      .filter((col) => col.id !== "selection") // Exclude selection column
      .map((col) => ({
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        id: (col.id || (col as any).accessorKey) as string,
        // Use header if available, otherwise use id or accessorKey
        label:
          typeof col.header === "string"
            ? col.header
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
              ((col.id || (col as any).accessorKey) as string),
      }));
  }, [columns]);

  // Fix type error with data
  const exportData = useMemo(() => {
    // Convert data to Record<string, unknown>[] type
    return data.map((item) => {
      // Convert each object to a Record with string keys
      const record: Record<string, unknown> = {};
      for (const key in item) {
        // Fix the 'any' type error by using proper typing
        record[key] = (item as Record<string, unknown>)[key];
        // if the field is an array, stringify it so it shows up
        if (Array.isArray(record[key]) && record[key].length > 0) {
          record[key] = record[key]
            .map((item: Record<string, unknown>) =>
              Object.entries(item)
                .map(([k, v]) => `${k}: ${v}`)
                .join(", "),
            )
            .join(" | ");
        }
      }
      return record;
    });
  }, [data]);

  return (
    <div
      data-testid="datagrid-container"
      className={`${styles.datagridContainer} ${className || ""} ${
        isLoading ? styles.disabled : ""
      }`}
    >
      {showHeader && (
        <DatagridHeader
          data-testid="data-grid-header"
          globalFilter={globalFilter}
          onGlobalFilterChange={setGlobalFilter}
          columnVisibility={columnVisibility}
          onColumnVisibilityChange={setColumnVisibility}
          columns={visibleColumns}
          density={density}
          onDensityChange={setDensity}
          onExport={showExportButton ? handleExport : undefined}
          exportOptions={exportOptions}
          onExportAction={onExportAction}
          onRefreshClick={showRefreshButton ? onRefreshClick : undefined}
          selectedRowCount={selectedRowCount}
          onClearSelection={enableSelection ? handleClearSelection : undefined}
        />
      )}

      <div className={styles.tableContainer}>
        {isError && renderError()}
        {isNoData && renderNoData()}
        {!isError && !isNoData && renderTable()}
      </div>

      {isLoading && renderLoading()}

      {showPagination && (
        <DatagridPagination
          data-testid="data-grid-pagination"
          pageIndex={table.getState().pagination.pageIndex}
          pageSize={table.getState().pagination.pageSize}
          totalRows={actualTotalRows}
          onPaginationChange={table.setPagination}
        />
      )}

      {/* Export Modal */}
      {mode === "client" && (
        <DatagridExportModal
          isOpen={isExportModalOpen}
          onClose={() => setIsExportModalOpen(false)}
          data={exportData}
          columns={exportColumns}
          fileName="table_export"
          selectedRows={enableSelection ? rowSelection : undefined}
          totalRows={data.length}
          columnVisibility={columnVisibility}
        />
      )}
    </div>
  );
}
