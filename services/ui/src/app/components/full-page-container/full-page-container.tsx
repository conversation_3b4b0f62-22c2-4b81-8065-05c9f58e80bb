import { FC, PropsWithChildren } from "react";
import styles from "./full-page-container.module.scss";

/**
 * A container component that provides a full-page layout with consistent spacing.
 * This way, there is no need to manually calculate the height of the container or use inline styling.
 * It is useful for views that have a fixed height, such as the inventory list view.
 * It is not useful for views that have a dynamic height, such as dashboard views.
 *
 * Features:
 * - Takes up full viewport height minus 96px (48px header + 48px view bar)
 * - Provides 1rem padding on all sides
 * - Accepts any valid React children as content
 *
 * @example
 * ```tsx
 * <FullPageContainer>
 *   <YourPageContent />
 * </FullPageContainer>
 * ```
 */
export const FullPageContainer: FC<PropsWithChildren> = ({ children }) => {
  return <div className={styles.container}>{children}</div>;
};

export default FullPageContainer;
