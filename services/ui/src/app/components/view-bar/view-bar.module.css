.viewBar {
  width: 100%;
  background-color: var(--cds-layer);
  height: 48px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--cds-border-subtle);
}

.viewBarInner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 1rem;
}

.titleGroup {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1em;
  margin: 0;
  width: 100%;  
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.actionsGroup {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dateRangeSelect {
  display: flex;
  align-items: center;
  position: relative;
}

.dateRangeIcon {
  position: absolute;
  left: 1rem;
  z-index: 1;
  pointer-events: none;
}

/* Target the Carbon Select to add left padding for the icon */
.dateRangeSelect :global(.cds--select-input) {
  padding-left: 2.5rem;
}

/* Remove the default Carbon Select icon if needed */
.dateRangeSelect :global(.cds--select__arrow) {
  right: 1rem;
}

.selectedMenuItem {
  background-color: var(--cds-background-selected);
}
