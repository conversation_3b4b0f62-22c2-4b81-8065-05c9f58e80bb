import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { ChangeEvent, useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import { ViewBar } from "../../components/view-bar/view-bar";
import { ContainerListTable } from "./components/container-list-table/container-list-table";
import type { ContainerItem, SortField } from "./types";
import { useTranslation } from "react-i18next";
import { Checkbox, Tag } from "@carbon/react";
import styles from "./container-list.module.css";
import { ExportModal } from "../../components/export-modal/export-modal";
import { authService } from "../../auth/auth-service";
import { useApiErrorState } from "../../../app/hooks/use-api-error-state";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";

export function ContainerList() {
  const [lastUpdated, setLastUpdated] = useState<number | null>(null);
  const location = useLocation();
  const navigate = useNavigate();
  const [bypassFiltering, setBypassFiltering] = useState(false);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });

  const [sorting, setSorting] = useState<SortingState>([
    { id: "container_id", desc: false },
  ]);

  // Get the SKU from the URL query parameters
  const queryParams = useMemo(
    () => new URLSearchParams(location.search),
    [location.search],
  );
  const skuParam = queryParams.get("sku");

  // Initialize column filters with the SKU filter if provided in URL
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(() => {
    const initialFilters: ColumnFiltersState = [];

    // If SKU parameter is provided in the URL, add it as a filter
    if (skuParam) {
      initialFilters.push({
        id: "sku",
        value: skuParam,
      });
    }

    return initialFilters;
  });

  // Update column filters when SKU parameter changes
  useEffect(() => {
    if (skuParam) {
      setColumnFilters((prev) => {
        // Check if we already have a SKU filter
        const skuFilterIndex = prev.findIndex((filter) => filter.id === "sku");

        if (skuFilterIndex >= 0) {
          // Update existing SKU filter
          const newFilters = [...prev];
          newFilters[skuFilterIndex] = {
            id: "sku",
            value: skuParam,
          };
          return newFilters;
        }

        // Add new SKU filter
        return [
          ...prev,
          {
            id: "sku",
            value: skuParam,
          },
        ];
      });
    }
  }, [skuParam]);

  // Handle dismissing the SKU tag
  const handleDismissSku = () => {
    // Remove the SKU filter from columnFilters
    setColumnFilters((prev) => prev.filter((filter) => filter.id !== "sku"));

    // Remove the SKU from URL query params
    const newParams = new URLSearchParams(location.search);
    newParams.delete("sku");
    navigate({ search: newParams.toString() });
  };

  const sortFields: SortField[] = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );

  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return [
      { id: "container_id", header: "Container ID" },
      { id: "location_id", header: "Location ID" },
      { id: "zone", header: "Zone" },
      { id: "sku", header: "SKU" },
      { id: "quantity", header: "Quantity" },
      {
        id: "last_activity_date",
        header: "Last Activity",
      },
      {
        id: "last_cycle_count",
        header: "Last Cycle Count",
      },
      { id: "data_updated", header: "Data Updated" },
      {
        id: "free_cycle_count",
        header: "Free Cycle Count",
      },
    ];
  };

  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );

  const { data, dataUpdatedAt, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      "/inventory/containers/list",
      {
        body: {
          limit: pagination.pageSize,
          page: pagination.pageIndex,
          filters: apiFilters,
          sortFields,
          byPassConfigSetting: bypassFiltering,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
      },
    );

  const isNoDataAvailable = useApiErrorState(error);

  const containerData = useMemo(
    () => (data?.data ?? []) as unknown as ContainerItem[],
    [data],
  );

  useEffect(() => {
    if (dataUpdatedAt) {
      setLastUpdated(dataUpdatedAt);
    }
  }, [dataUpdatedAt]);

  const { t } = useTranslation();

  const formattedLastUpdated = lastUpdated
    ? new Date(lastUpdated).toLocaleString()
    : t("containerList.loading", "Loading...");

  const handleRefresh = () => {
    refetch();
  };

  // Add state for the export dialog
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");
    const accessToken = await authService.getAccessToken();
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
    const exportUrl = `${API_BASE_URL}/inventory/containers/list/export`;

    // Create request body with only selected columns
    const requestBody = {
      columns: selectedColumns,
      filters: apiFilters,
      byPassConfigSetting: bypassFiltering,
    };

    const response = await fetch(exportUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
      signal,
    });

    if (!response.ok) {
      throw new Error(`Export failed with status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  return (
    <div data-testid="container-list" className={styles.container}>
      <ViewBar title={"Container List"} showDatePeriodRange={false}>
        <div className={styles.controls}>
          {skuParam && (
            <div className={styles.filterTag}>
              <span>Filter:</span>
              <Tag type="high-contrast" filter onClose={handleDismissSku}>
                SKU: {skuParam}
              </Tag>
            </div>
          )}
          <Checkbox
            className={styles.checkbox}
            labelText={
              bypassFiltering
                ? "Uncheck this for automation zone filtering"
                : "Check this to reset automation zone filtering"
            }
            id="bypass-filtering-checkbox"
            checked={bypassFiltering}
            onChange={(evt: ChangeEvent<HTMLInputElement>) =>
              setBypassFiltering(evt.target.checked)
            }
          />
          <p
            className={styles.lastUpdated}
            data-testid="container-list-last-update"
          >
            {t(
              "containerList.lastUpdated:",
              "Last Updated: {{formattedLastUpdated}}",
              { formattedLastUpdated: formattedLastUpdated },
            )}
          </p>
        </div>
      </ViewBar>
      <FullPageContainer>
        <ContainerListTable
          data={containerData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          error={isNoDataAvailable ? null : error}
          rowCount={data?.metadata.totalResults ?? 0}
          onRefresh={handleRefresh}
          onExport={() => setExportDialogOpen(true)}
          setGlobalFilter={setGlobalFilter}
        />
      </FullPageContainer>
      <ExportModal
        open={exportDialogOpen}
        onClose={handleExportModalClose}
        onExport={handleExport}
        filters={columnFilters}
        columnDefs={getColumnDefinitions()}
        baseFileName="Containers_Forward-Pick_"
      />
    </div>
  );
}

export default ContainerList;
