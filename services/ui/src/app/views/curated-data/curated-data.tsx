import { useState } from "react";
import { ViewBar } from "../../components/view-bar/view-bar";
import { CuratedDataTablePicker } from "./components/curated-data-table-picker/curated-data-table-picker";
import { CuratedDataTable } from "./components/curated-data-table/curated-data-table";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";

export function CuratedData() {
  const [tableId, setTableId] = useState<string>("bronze_bin_utilization");

  return (
    <div style={{ flex: 1, width: "100%" }} data-testid="curated-data-table">
      <ViewBar title={"Curated Data"} showDatePeriodRange={false}>
        <CuratedDataTablePicker onTableChange={setTableId} />
      </ViewBar>
      <FullPageContainer>
        <CuratedDataTable tableId={tableId} />
      </FullPageContainer>
    </div>
  );
}

export default CuratedData;
