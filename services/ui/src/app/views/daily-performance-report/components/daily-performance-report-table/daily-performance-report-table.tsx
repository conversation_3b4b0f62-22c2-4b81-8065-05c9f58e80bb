import { createColumnHelper } from "@tanstack/react-table";
import { useMemo } from "react";
import type { components } from "@ict/sdk/openapi-react-query";
import { Datagrid } from "../../../../components/datagrid";
import { useTranslation } from "react-i18next";
type WorkstationDailyPerformanceList =
  components["schemas"]["WorkstationDailyPerformanceList"];
interface DailyPerformanceReportTableProps {
  data: WorkstationDailyPerformanceList;
  loading: boolean;
  error: Error | null;
  onRefresh?: () => void;
}

export function DailyPerformanceReportTable({
  data,
  loading,
  error,
  onRefresh,
}: DailyPerformanceReportTableProps) {
  // Transform the data to flatten it for the table
  const tableData = useMemo(() => {
    return data.map((row) => ({
      date: new Date(row.date).toLocaleDateString(),
      totalLoggedInHours: Number(row.totalLoggedInHours).toFixed(1),
      idlePercentage: row.idlePercentage,
      starvedPercentage: row.starvedPercentage,
      starvedHours: Number(row.starvedHours).toFixed(1),
      donorContainers: row.donorContainers ?? "-",
      gtpContainers: row.gtpContainers ?? "-",
      linesPicked: row.linesPicked ?? "-",
      qtyPerLine: row.qtyPerLine?.toFixed(1) ?? "-",
      pickLineQty: row.pickLineQty ?? "-",
      linesPerHour: row.linesPerHour?.toFixed(1) ?? "-",
      avgLinesPickedPerHr1stShiftPercentage:
        row.avgLinesPickedPerHr1stShiftPercentage ?? "-",
      avgLinesPickedPerHr2ndShiftPercentage:
        row.avgLinesPickedPerHr2ndShiftPercentage ?? "-",
      retrievalFromDMS: row.retrievalFromDMS ?? "-",
      storageToDMS: row.storageToDMS ?? "-",
      retrievalFromASRS: row.retrievalFromASRS ?? "-",
      storageToASRS: row.storageToASRS ?? "-",
    }));
  }, [data]);

  // Define column helper for type safety
  const columnHelper = createColumnHelper<Record<string, unknown>>();

  const { t } = useTranslation(); // Get the t function

  const columns = useMemo(
    () => [
      columnHelper.accessor("date", {
        header: t("dailyPerformanceTable.date", "Date"),
        size: 100,
      }),
      columnHelper.accessor("totalLoggedInHours", {
        header: t("dailyPerformanceTable.totalHours", "Total Hours"),
        size: 100,
      }),
      columnHelper.accessor("idlePercentage", {
        header: t("dailyPerformanceTable.idlePercent", "Idle %"),
        size: 80,
      }),
      columnHelper.accessor("starvedPercentage", {
        header: t("dailyPerformanceTable.starvedPercent", "Starved %"),
        size: 90,
      }),
      columnHelper.accessor("starvedHours", {
        header: t("dailyPerformanceTable.starvedHours", "Starved Hours"),
        size: 110,
      }),
      columnHelper.accessor("donorContainers", {
        header: t("dailyPerformanceTable.donorContainers", "Donor Containers"),
        size: 130,
      }),
      columnHelper.accessor("gtpContainers", {
        header: t("dailyPerformanceTable.gtpContainers", "GTP Containers"),
        size: 120,
      }),
      columnHelper.accessor("linesPicked", {
        header: t("dailyPerformanceTable.linesPicked", "Lines Picked"),
        size: 110,
      }),
      columnHelper.accessor("qtyPerLine", {
        header: t("dailyPerformanceTable.qtyPerLine", "Qty/Line"),
        size: 90,
      }),
      columnHelper.accessor("pickLineQty", {
        header: t("dailyPerformanceTable.pickLineQty", "Pick Line Qty"),
        size: 120,
      }),
      columnHelper.accessor("linesPerHour", {
        header: t("dailyPerformanceTable.linesPerHour", "Lines/Hour"),
        size: 100,
      }),
      columnHelper.accessor("avgLinesPickedPerHr1stShiftPercentage", {
        header: t("dailyPerformanceTable.firstShiftPercent", "1st Shift %"),
        size: 100,
      }),
      columnHelper.accessor("avgLinesPickedPerHr2ndShiftPercentage", {
        header: t("dailyPerformanceTable.secondShiftPercent", "2nd Shift %"),
        size: 100,
      }),
      columnHelper.accessor("retrievalFromDMS", {
        header: t("dailyPerformanceTable.dmsRetrieval", "DMS Retrieval"),
        size: 120,
      }),
      columnHelper.accessor("storageToDMS", {
        header: t("dailyPerformanceTable.dmsStorage", "DMS Storage"),
        size: 110,
      }),
      columnHelper.accessor("retrievalFromASRS", {
        header: t("dailyPerformanceTable.asrsRetrieval", "ASRS Retrieval"),
        size: 120,
      }),
      columnHelper.accessor("storageToASRS", {
        header: t("dailyPerformanceTable.asrsStorage", "ASRS Storage"),
        size: 110,
      }),
    ],
    [columnHelper, t],
  );

  return (
    <Datagrid
      columns={columns}
      isLoading={loading}
      error={error ? "Error loading data" : undefined}
      data={tableData}
      mode="client"
      enableSelection={false}
      showPagination={false}
      initialDensity="default"
      showRefreshButton={!!onRefresh}
      onRefreshClick={onRefresh}
    />
  );
}
