import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import { ViewBar } from "../../components/view-bar/view-bar";
import { InventoryForecastListTable } from "./components/inventory-forecast-list-table/inventory-forecast-list-table";
import {
  SortField,
  InventoryForecastItem,
} from "./components/inventory-forecast-list-table/types";
import { InventoryForecastTimestamp } from "./components/inventory-forecast-timestamp/inventory-forecast-timestamp";
import { InventoryForecastTimestamp as InventoryForecastTimestampType } from "./components/inventory-forecast-timestamp/types";
import { useApiErrorState } from "../../../app/hooks/use-api-error-state";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";

export default function InventoryForecast() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });
  const [sorting, setSorting] = useState<SortingState>([
    // Default sort by forwardPickTomorrow
    { id: "forwardPickTomorrow", desc: false },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const sortFields: SortField[] = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );
  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );
  // Use the useQuery hook to fetch data
  const {
    data: listData,
    error: listError,
    isLoading: isListLoading,
    isFetching: isListFetching,
    refetch: refetchList,
  } = ictApi.client.useQuery(
    "post",
    "/inventory/forecast/list",
    {
      body: {
        limit: pagination.pageSize,
        page: pagination.pageIndex,
        filters: apiFilters,
        sortFields,
        ...(globalFilter !== "" && { searchString: globalFilter }),
      },
    },
    {
      enabled: true,
      keepPreviousData: true,
      placeholderData: (prev) => prev,
      retry: false,
      refetchOnWindowFocus: false,
    },
  );

  // Fetch timestamp data
  const {
    data: timestampData,
    isLoading: isTimestampLoading,
    error: timestampError,
  } = ictApi.client.useQuery(
    "get",
    "/inventory/forecast/data-analysis-timestamp",
    {},
    {
      enabled: true,
      retry: false,
      refetchOnWindowFocus: false,
    },
  );

  const isNoDataAvailable = useApiErrorState(listError || timestampError);

  const inventoryForecastData = useMemo(
    () => (listData?.data ?? []) as unknown as InventoryForecastItem[],
    [listData],
  );
  const rawTimestamps = useMemo(() => {
    if (timestampData) {
      const tsData = timestampData as InventoryForecastTimestampType;
      return {
        inventoryUpdatedAt: tsData.dataUpdateTimestamp,
        forecastPerformedAt: tsData.analysisPerformedTimestamp,
      };
    }
    return { inventoryUpdatedAt: undefined, forecastPerformedAt: undefined };
  }, [timestampData]);
  // Handle refresh click
  const handleRefresh = () => {
    refetchList();
  };

  return (
    <div
      data-testid="ict-inventory-forecast"
      style={{ flex: 1, width: "100%" }}
    >
      <ViewBar title={"Inventory Forecast"} showDatePeriodRange={false}>
        <InventoryForecastTimestamp
          isLoading={isTimestampLoading}
          error={timestampError}
          inventoryUpdatedAt={rawTimestamps.inventoryUpdatedAt}
          forecastPerformedAt={rawTimestamps.forecastPerformedAt}
        />
      </ViewBar>
      <FullPageContainer>
        <InventoryForecastListTable
          data={inventoryForecastData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isListLoading || isTimestampLoading}
          isFetching={isListFetching}
          error={
            isNoDataAvailable
              ? undefined
              : listError || timestampError
                ? "Error fetching data"
                : ""
          }
          rowCount={listData?.metadata.totalResults ?? 0}
          onRefresh={handleRefresh}
          inventoryUpdatedAt={rawTimestamps.inventoryUpdatedAt}
          forecastPerformedAt={rawTimestamps.forecastPerformedAt}
          setGlobalFilter={setGlobalFilter}
        />
      </FullPageContainer>
    </div>
  );
}
