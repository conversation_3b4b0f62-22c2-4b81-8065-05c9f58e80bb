import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { useEffect, useMemo, useState } from "react";
import { ictApi } from "../../api/ict-api";
import { transformFilters } from "../../api/util/filter-transform-util";
import { ViewBar } from "../../components/view-bar/view-bar";
import { InventoryListTable } from "./components/inventory-list-table/inventory-list-table";
import type { InventoryItem, SortField } from "./types";
import { useTranslation } from "react-i18next";
import { authService } from "../../auth/auth-service";
import { ExportModal } from "../../components/export-modal/export-modal";
import { useApiErrorState } from "../../../app/hooks/use-api-error-state";
import { FullPageContainer } from "../../components/full-page-container/full-page-container";

export function InventoryList() {
  const [lastUpdated, setLastUpdated] = useState<number | null>(null);

  // State that was previously in the table component
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50,
  });

  const [sorting, setSorting] = useState<SortingState>([
    // Default sort by days on hand
    { id: "daysOnHand", desc: false },
  ]);

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const sortFields: SortField[] = useMemo(
    () =>
      sorting.map((sort) => ({
        columnName: sort.id,
        isDescending: sort.desc,
      })),
    [sorting],
  );
  // Function to generate column definitions for the export dialog
  const getColumnDefinitions = () => {
    return [
      { id: "sku", header: "Item" },
      {
        id: "quantityAvailable",
        header: "Quantity Available",
      },
      {
        id: "quantityAllocated",
        header: "Quantity Allocated",
      },
      {
        id: "daysOnHand",
        header: "Days on Hand (DOH)",
      },
      {
        id: "averageDailyQuantity",
        header: "Avg. Daily Qty.",
      },
      {
        id: "averageDailyOrders",
        header: "Avg. Daily Orders",
      },
      { id: "skuPositions", header: "SKU Positions" },
      {
        id: "latestActivityDateTimestamp",
        header: "Last Activity Date",
      },
      {
        id: "latestCycleCountTimestamp",
        header: "Cycle Count Date",
      },
      { id: "description", header: "Description" },
      {
        id: "targetMultiplicity",
        header: "Target Multiplicity",
      },
      {
        id: "velocityClassification",
        header: "Velocity Classification",
      },
    ];
  };

  const apiFilters = useMemo(
    () => transformFilters(columnFilters),
    [columnFilters],
  );

  // Use the useQuery hook to fetch data
  const { data, dataUpdatedAt, error, isLoading, isFetching, refetch } =
    ictApi.client.useQuery(
      "post",
      "/inventory/skus/list",
      {
        body: {
          limit: pagination.pageSize,
          page: pagination.pageIndex,
          filters: apiFilters,
          sortFields,
          ...(globalFilter !== "" && { searchString: globalFilter }),
        },
      },
      {
        enabled: true,
        keepPreviousData: true,
        placeholderData: (prev) => prev,
        retry: false,
        refetchOnWindowFocus: false,
      },
    );

  const isNoDataAvailable = useApiErrorState(error);

  // Cast the data to the correct type
  const inventoryData = useMemo(
    () => (data?.data ?? []) as unknown as InventoryItem[],
    [data],
  );

  // Update the last updated timestamp when data is updated
  useEffect(() => {
    if (dataUpdatedAt) {
      setLastUpdated(dataUpdatedAt);
    }
  }, [dataUpdatedAt]);

  const { t } = useTranslation();

  // Format the last updated timestamp
  const formattedLastUpdated = lastUpdated
    ? new Date(lastUpdated).toLocaleString()
    : t("inventoryList.loading", "Loading...");
  // Add state for the export dialog
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  // Export handler to use selected columns
  const handleExport = async (
    fileName: string,
    selectedColumns: Record<string, boolean>,
    signal: AbortSignal,
  ) => {
    console.log("Starting export process...");
    const accessToken = await authService.getAccessToken();
    const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
    const exportUrl = `${API_BASE_URL}/inventory/skus/list/export`;
    console.log("Export URL:", exportUrl);

    // Create request body with only selected columns
    const requestBody = {
      columns: selectedColumns,
      filters: apiFilters,
    };

    const response = await fetch(exportUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
      // Add the signal to the fetch request for possible export cancellation
      signal,
    });

    if (!response.ok) {
      throw new Error(`Export failed with status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    console.log("Export completed successfully!");
  };

  // Add a function to handle modal close
  const handleExportModalClose = () => {
    setExportDialogOpen(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <div data-testid="inventory-list" style={{ flex: 1, width: "100%" }}>
      <ViewBar title={"Inventory List"} showDatePeriodRange={false}>
        <p
          data-testid="inventory-list-last-update"
          className="cds--type-body-01"
          style={{ color: "#6f6f6f", marginRight: "1rem" }}
        >
          {t(
            "inventoryList.lastUpdated",
            "Last Updated: {{formattedLastUpdated}}",
            { formattedLastUpdated: formattedLastUpdated },
          )}
        </p>
      </ViewBar>
      <FullPageContainer>
        <InventoryListTable
          data-testid="inventory-list-table"
          data={inventoryData}
          pagination={pagination}
          setPagination={setPagination}
          sorting={sorting}
          setSorting={setSorting}
          columnFilters={columnFilters}
          setColumnFilters={setColumnFilters}
          isLoading={isLoading}
          isFetching={isFetching}
          onExport={() => setExportDialogOpen(true)}
          onRefresh={handleRefresh}
          error={
            isNoDataAvailable ? undefined : error ? "Error fetching data" : ""
          }
          rowCount={data?.metadata.totalResults ?? 0}
          setGlobalFilter={setGlobalFilter}
        />
        <ExportModal
          open={exportDialogOpen}
          onClose={handleExportModalClose}
          onExport={handleExport}
          filters={columnFilters}
          columnDefs={getColumnDefinitions()}
          baseFileName="SKUs_Forward-Pick"
        />
      </FullPageContainer>
    </div>
  );
}

export default InventoryList;
