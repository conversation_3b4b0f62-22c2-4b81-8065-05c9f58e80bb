import { Link } from "@carbon/react";
import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useCallback } from "react";
import { useNavigate } from "react-router";
import { Datagrid } from "../../../../components/datagrid";
import type { FilterState } from "../../../../components/datagrid/types";
import type { InventoryItem } from "../../types";
import { useTranslation } from "react-i18next";
interface InventoryListTableProps {
  data: InventoryItem[];
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
  sorting: SortingState;
  setSorting: (sorting: SortingState) => void;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState) => void;
  isLoading: boolean;
  isFetching: boolean;
  error: unknown;
  rowCount: number;
  onExport: () => void;
  onRefresh?: () => void;
  setGlobalFilter: (globalFilter: string) => void;
}

export function InventoryListTable({
  data,
  pagination,
  setPagination,
  sorting,
  setSorting,
  // columnFilters,
  setColumnFilters,
  isLoading,
  isFetching,
  error,
  rowCount,
  onExport,
  onRefresh,
  setGlobalFilter,
}: InventoryListTableProps) {
  const navigate = useNavigate();

  // Handle SKU click to navigate to container list with SKU filter
  const handleSkuClick = useCallback(
    (sku: string) => {
      navigate(`/ict-container-list?sku=${encodeURIComponent(sku)}`);
    },
    [navigate],
  );

  // Define columns using createColumnHelper
  const columnHelper = createColumnHelper<InventoryItem>();
  const { t } = useTranslation();

  const columns = [
    columnHelper.accessor("sku", {
      header: t("inventoryListTable.sku", "SKU"),
      size: 150,
      cell: (info) => (
        <Link
          onClick={(e) => {
            e.stopPropagation();
            handleSkuClick(info.getValue());
          }}
          style={{ cursor: "pointer" }}
        >
          {info.getValue()}
        </Link>
      ),
    }),
    columnHelper.accessor("description", {
      header: t("inventoryListTable.description", "Description"),
      size: 200,
    }),
    columnHelper.accessor("daysOnHand", {
      header: t("inventoryListTable.daysOnHand", "Days On Hand"),
      size: 120,
    }),
    columnHelper.accessor("averageDailyQuantity", {
      header: t("inventoryListTable.avgDailyQty", "Avg Daily Qty"),
      size: 120,
    }),
    columnHelper.accessor("averageDailyOrders", {
      header: t("inventoryListTable.avgDailyOrders", "Avg Daily Orders"),
      size: 140,
    }),
    columnHelper.accessor("totalQuantity", {
      header: t("inventoryListTable.totalQuantity", "Total Quantity"),
      size: 120,
    }),
    columnHelper.accessor("quantityAvailable", {
      header: t("inventoryListTable.qtyAvailable", "Qty Available"),
      size: 120,
    }),
    columnHelper.accessor("locations", {
      header: t("inventoryListTable.locations", "Locations"),
      size: 100,
    }),
    columnHelper.accessor("status", {
      header: t("inventoryListTable.status", "Status"),
      size: 100,
    }),
    columnHelper.accessor("latestInventorySnapshotTimestamp", {
      header: t(
        "inventoryListTable.latestInventorySnapshot",
        "Latest Inventory Snapshot",
      ),
      size: 200,
      cell: (info) => {
        const value = info.getValue();
        return value ? new Date(value).toLocaleString() : "";
      },
    }),
    columnHelper.accessor("latestActivityDateTimestamp", {
      header: t("inventoryListTable.latestActivity", "Latest Activity"),
      size: 200,
      cell: (info) => {
        const value = info.getValue();
        return value ? new Date(value).toLocaleString() : "";
      },
    }),
  ];

  // Handlers for Datagrid callbacks
  const handlePageChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    [setPagination],
  );

  const handleSort = useCallback(
    (newSorting: SortingState) => {
      setSorting(newSorting);
    },
    [setSorting],
  );

  const handleFilter = useCallback(
    (newFilters: FilterState) => {
      // Convert FilterState to ColumnFiltersState
      const newColumnFilters: ColumnFiltersState = Object.entries(
        newFilters.filters,
      ).map(([id, value]) => ({
        id,
        value,
      }));

      setColumnFilters(newColumnFilters);
      setGlobalFilter(newFilters.globalFilter);
    },
    [setColumnFilters, setGlobalFilter],
  );

  return (
    <Datagrid
      columns={columns}
      data={data}
      mode="server"
      totalRows={rowCount}
      isLoading={isLoading || isFetching}
      error={error ? String(error) : undefined}
      onPageChange={handlePageChange}
      onSort={handleSort}
      onFilter={handleFilter}
      onExport={onExport}
      onRefreshClick={onRefresh}
      showExportButton={true}
      showRefreshButton={!!onRefresh}
      initialPagination={pagination}
      initialSorting={sorting}
      enableSelection={false}
    />
  );
}
