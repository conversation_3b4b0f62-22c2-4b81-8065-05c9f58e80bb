import { useMediaQuery } from "@uidotdev/usehooks";
import { useCallback, useEffect, useState } from "react";
import {
  type Layout,
  Responsive,
  ResponsiveProps,
  WidthProvider,
  WidthProviderProps,
} from "react-grid-layout";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import { Widget } from "../../../widgets/widget";
import type {
  BaseWidgetProps,
  WidgetFilters,
} from "../../../widgets/widget.types";
import classes from "./react-grid-layout.module.css";

const ResponsiveGridLayout = WidthProvider(Responsive) as unknown as React.FC<
  ResponsiveProps & WidthProviderProps
>;

/**
 * Return the bottom coordinate of the layout.
 *
 * @param  {Array} layout Layout array.
 * @return {Number}       Bottom coordinate.
 */
export function bottom(layout: DashboardWidget[]): number {
  let max = 0;
  for (let i = 0, len = layout.length; i < len; i++) {
    const bottomY = (layout[i]?.y ?? 0) + (layout[i]?.h ?? 0);
    if (bottomY > max) max = bottomY;
  }
  return max;
}

interface DashboardGridLayoutProps {
  items: DashboardWidget[];
  filters?: WidgetFilters;
  canEdit?: boolean;
  onShowAside?: (content: React.ReactNode) => void;
  onUpdateDashboard?: (updatedContent: DashboardWidget[]) => void;
  onDeleteWidget?: (id: string) => void;
}

export type DashboardWidget = {
  content: BaseWidgetProps;
  x?: number;
  y?: number;
  w?: number;
  h?: number;
};

export const DashboardGridLayout = ({
  items,
  filters,
  onShowAside,
  onUpdateDashboard,
  onDeleteWidget,
  canEdit = false,
}: DashboardGridLayoutProps) => {
  const isMobile = useMediaQuery("(max-width: 1100px)");
  // Track only if this is the initial render to prevent layout jumps
  const [isInitialRender, setIsInitialRender] = useState(true);

  // Convert items to layout format - this is now a derived value, not state
  const layouts = useCallback(() => {
    // Ensure all items have valid x, y, w, h values
    const validItems = items.map((item) => ({
      ...item,
      x: typeof item.x === "number" ? item.x : 0,
      y: typeof item.y === "number" ? item.y : bottom(items),
      w: typeof item.w === "number" ? item.w : 4,
      h: typeof item.h === "number" ? item.h : 2,
    }));

    return {
      lg: validItems.map((item) => ({
        i: item.content?.id || "",
        x: item.x,
        y: item.y,
        w: item.w,
        h: item.h,
        minW: 2,
        minH: 2,
      })),
    };
  }, [items]);

  // Set initial render to false after component mounts
  useEffect(() => {
    if (isInitialRender) {
      // This is a bit hacky but we do not want to invoke a "layout change" when we first
      // load the dashboard. Use a timeout to prevent this from happening.
      const timer = setTimeout(() => {
        setIsInitialRender(false);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isInitialRender]);

  // Handle widget option updates
  const handleUpdateWidgetOptions = (
    widgetId: string,
    newOptions: Record<string, unknown>,
  ) => {
    // Create a deep copy of the items array
    const updatedItems = items.map((widget) => {
      // If this is the widget we want to update
      if (widget.content?.id === widgetId) {
        return {
          ...widget,
          content: {
            ...widget.content,
            options: {
              ...widget.content.options,
              ...newOptions,
            },
          },
        };
      }
      // Otherwise return the widget unchanged
      return widget;
    });

    // Update the entire dashboard
    onUpdateDashboard?.(updatedItems);
  };

  // Handle layout changes (drag, resize)
  const handleLayoutChange = (
    _currentLayout: Layout[],
    allLayouts: { [key: string]: Layout[] },
  ) => {
    // Skip the initial layout change event which is triggered on mount
    if (isInitialRender) {
      return;
    }

    // Get the current layout (we're using 'lg' as the main layout)
    const currentLayout = allLayouts.lg;

    if (!currentLayout || currentLayout.length === 0) {
      return;
    }

    // Create a map of layout items by ID for easy lookup
    const layoutMap = new Map(
      currentLayout.map((layoutItem) => [layoutItem.i, layoutItem]),
    );

    // Check if there's an actual layout change
    let hasLayoutChanged = false;

    // Update the items with the new layout information
    const updatedItems = items.map((item) => {
      const id = item.content?.id || "";
      const layoutItem = layoutMap.get(id);

      if (layoutItem) {
        // Check if this item's position or size has changed
        if (
          item.x !== layoutItem.x ||
          item.y !== layoutItem.y ||
          item.w !== layoutItem.w ||
          item.h !== layoutItem.h
        ) {
          hasLayoutChanged = true;
          return {
            ...item,
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          };
        }
      }
      return item;
    });

    // Only update the dashboard if there was an actual change
    if (hasLayoutChanged) {
      onUpdateDashboard?.(updatedItems);
    }
  };

  const handleDeleteWidget = (id: string) => {
    onDeleteWidget?.(id);
  };

  return (
    <div className={classes.gridContainer}>
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts()}
        breakpoints={{ lg: 900, sm: 0 }}
        cols={{ lg: 12, sm: 1 }}
        rowHeight={100}
        containerPadding={[16, 16]}
        margin={{
          lg: [16, 16],
          md: [16, 16],
          sm: [16, 16],
          xs: [16, 16],
          xxs: [16, 16],
        }}
        isDraggable={!isMobile && canEdit}
        isResizable={!isMobile && canEdit}
        onLayoutChange={handleLayoutChange}
        useCSSTransforms={true}
      >
        {items.map((item) => (
          <div key={item.content?.id} className={classes.widgetContainer}>
            <Widget
              id={item.content?.id || ""}
              type={(item.content?.type as string) || ""}
              options={item.content?.options || {}}
              filters={filters}
              onShowAside={onShowAside}
              onUpdateOptions={handleUpdateWidgetOptions}
              onDeleteWidget={handleDeleteWidget}
            />
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  );
};
