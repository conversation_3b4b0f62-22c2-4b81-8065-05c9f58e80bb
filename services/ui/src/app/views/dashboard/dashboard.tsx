import { Add } from "@carbon/icons-react";
import { But<PERSON> } from "@carbon/react";
import { useEffect, useState } from "react";
import { useOutletContext } from "react-router";
import type { components } from "@ict/sdk/openapi-react-query";
import { useRoles } from "../../auth/hooks/use-roles";
import { ViewBar } from "../../components/view-bar/view-bar";
import { useViewOptions } from "../../hooks/use-view-options";
import { DatePeriod, type DatePeriodRange } from "../../types";
import type { BaseViewProps } from "../view-registry.types";
import { AddWidgetDialog } from "./components/add-widget-dialog";
import { DashboardSettings } from "./components/dashboard-settings";
import classes from "./dashboard.module.css";
import { useDashboardWidgets } from "./hooks/use-dashboard-widgets";
import {
  DashboardGridLayout,
  type DashboardWidget,
} from "./react-grid-layout/react-grid-layout";
type AppConfigSetting = components["schemas"]["AppConfigSetting"];

type DashboardProps = BaseViewProps & {
  id: string;
  setting: AppConfigSetting;
};

export type DashboardOptions = {
  title: string;
  content: DashboardWidget[];
  defaultDatePeriodRange: DatePeriodRange;
  showDateRange: boolean;
};

export function Dashboard({ id, setting }: DashboardProps) {
  const { hasConfiguratorAccess } = useRoles();
  const handleShowViewAside =
    useOutletContext<(content: React.ReactNode) => void>();

  // Extract initial values from setting or use defaults
  const initialOptions: DashboardOptions =
    (setting?.value as DashboardOptions) || {
      title: "Dashboard",
      content: [],
      defaultDatePeriodRange: DatePeriod.today,
      showDateRange: true,
    };

  const initialSettings = setting || {
    name: id,
    dataType: "json",
    value: initialOptions,
  };

  const {
    draftOptions,
    handleShowSettings,
    handleOptionsChange,
    handleApplyOptions,
    handleSaveOptions,
    isDirty,
  } = useViewOptions<DashboardOptions>({
    setting: initialSettings,
    defaultOptions: initialOptions,
    optionsComponent: DashboardSettings,
    successMessage: "Your dashboard has been saved",
  });

  // Initialize with the default date period range from draftOptions
  const [selectedDatePeriodRange, setDatePeriodRange] =
    useState<DatePeriodRange>(
      draftOptions?.defaultDatePeriodRange || DatePeriod.today,
    );

  // Update selectedDatePeriodRange when defaultDatePeriodRange changes in draftOptions
  useEffect(() => {
    if (draftOptions?.defaultDatePeriodRange) {
      setDatePeriodRange(draftOptions.defaultDatePeriodRange);
    }
  }, [draftOptions?.defaultDatePeriodRange]);

  const { handleUpdateDashboard, handleAddWidget, handleDeleteWidget } =
    useDashboardWidgets({
      draftOptions,
      handleOptionsChange,
      handleApplyOptions,
      selectedDatePeriodRange,
    });

  const handleDatePeriodRangeChange = (range: DatePeriodRange) => {
    setDatePeriodRange(range);
  };

  const handleAddWidgetDialog = () => {
    handleShowViewAside(
      <AddWidgetDialog
        onAddWidget={handleAddWidget}
        onClose={() => handleShowViewAside(null)}
      />,
    );
  };

  // Early return if no content
  if (!draftOptions) {
    return null;
  }

  return (
    <div
      data-testid={id}
      style={{ flex: 1, overflowX: "hidden" }}
      className={classes.container}
    >
      <div className={classes.viewBarContainer}>
        <ViewBar
          title={draftOptions.title}
          hasConfiguratorAccess={hasConfiguratorAccess}
          showDatePeriodRange={draftOptions.showDateRange}
          selectedDatePeriodRange={selectedDatePeriodRange}
          onDatePeriodRangeChange={handleDatePeriodRangeChange}
          showSettings={true}
          saveEnabled={isDirty}
          showSave={hasConfiguratorAccess}
          onSettingsClick={handleShowSettings}
          onSaveClick={handleSaveOptions}
        >
          {hasConfiguratorAccess && (
            <Button
              renderIcon={Add}
              size="sm"
              kind="secondary"
              onClick={handleAddWidgetDialog}
            >
              Add Widget
            </Button>
          )}
        </ViewBar>
      </div>
      <div className={classes.dashboardContainer}>
        <DashboardGridLayout
          key={`react-grid-${id}`}
          items={draftOptions.content}
          filters={{ datePeriodRange: selectedDatePeriodRange }}
          onShowAside={handleShowViewAside}
          onUpdateDashboard={handleUpdateDashboard}
          onDeleteWidget={handleDeleteWidget}
          canEdit={hasConfiguratorAccess}
        />
      </div>
    </div>
  );
}

export default Dashboard;
